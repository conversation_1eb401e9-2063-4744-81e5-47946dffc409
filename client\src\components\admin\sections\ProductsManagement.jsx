import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  Badge,
  Image,
  Tooltip,
  Spin,
  DatePicker,
  Rate
} from 'antd';
import {
  ShoppingOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  StarOutlined,
  FilterOutlined,
  ReloadOutlined,
  FileTextOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { productsApi } from '../../../services/adminApi';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const ProductsManagement = () => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [pendingProducts, setPendingProducts] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    vendor: '',
    search: ''
  });
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [approvalAction, setApprovalAction] = useState('');
  const [stats, setStats] = useState({});
  const [activeTab, setActiveTab] = useState('all');

  const [approvalForm] = Form.useForm();

  useEffect(() => {
    fetchProducts();
    fetchPendingProducts();
    fetchStats();
  }, [pagination.current, pagination.pageSize, filters, activeTab]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters
      };

      const response = await productsApi.getAll(params);
      if (response.data.success) {
        setProducts(response.data.data.products);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination.totalProducts
        }));
      }
    } catch (error) {
      message.error('Failed to fetch products');
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPendingProducts = async () => {
    try {
      const response = await productsApi.getPendingApproval();
      if (response.data.success) {
        setPendingProducts(response.data.data.products);
      }
    } catch (error) {
      console.error('Failed to fetch pending products:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await productsApi.getStats();
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const handleApprovalAction = (product, action) => {
    setSelectedProduct(product);
    setApprovalAction(action);
    setApprovalModalVisible(true);
    
    if (action === 'approve') {
      approvalForm.setFieldsValue({ notes: '' });
    } else if (action === 'reject') {
      approvalForm.setFieldsValue({ reason: '', notes: '' });
    }
  };

  const handleApprovalSubmit = async (values) => {
    try {
      setLoading(true);
      let response;

      switch (approvalAction) {
        case 'approve':
          response = await productsApi.approve(selectedProduct._id, {
            notes: values.notes
          });
          break;
        case 'reject':
          response = await productsApi.reject(selectedProduct._id, {
            reason: values.reason,
            notes: values.notes
          });
          break;
        case 'request_changes':
          response = await productsApi.requestChanges(selectedProduct._id, {
            changes: values.changes,
            notes: values.notes
          });
          break;
        default:
          throw new Error('Invalid approval action');
      }

      if (response.data.success) {
        message.success(`Product ${approvalAction}d successfully`);
        setApprovalModalVisible(false);
        approvalForm.resetFields();
        fetchProducts();
        fetchPendingProducts();
      }
    } catch (error) {
      message.error(`Failed to ${approvalAction} product`);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (productId, newStatus) => {
    try {
      const response = await productsApi.updateStatus(productId, { status: newStatus });
      if (response.data.success) {
        message.success('Product status updated successfully');
        fetchProducts();
      }
    } catch (error) {
      message.error('Failed to update product status');
    }
  };

  const handleToggleFeatured = async (productId, featured) => {
    try {
      const response = await productsApi.toggleFeatured(productId, { featured: !featured });
      if (response.data.success) {
        message.success(`Product ${!featured ? 'featured' : 'unfeatured'} successfully`);
        fetchProducts();
      }
    } catch (error) {
      message.error('Failed to update featured status');
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      draft: 'default',
      pending_approval: 'processing',
      active: 'success',
      inactive: 'warning',
      rejected: 'error',
      archived: 'default'
    };
    return colors[status] || 'default';
  };

  const getApprovalStatusColor = (status) => {
    const colors = {
      pending: 'processing',
      approved: 'success',
      rejected: 'error',
      requires_changes: 'warning'
    };
    return colors[status] || 'default';
  };

  const productColumns = [
    {
      title: 'Product',
      key: 'product',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Image
            width={50}
            height={50}
            src={record.images?.[0]?.url || '/placeholder-product.png'}
            alt={record.name}
            style={{ borderRadius: '4px', objectFit: 'cover' }}
            fallback="/placeholder-product.png"
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: '4px' }}>
              {record.name}
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              SKU: {record.sku}
            </Text>
          </div>
        </div>
      ),
      width: 250
    },
    {
      title: 'Vendor',
      dataIndex: ['vendor', 'businessName'],
      key: 'vendor',
      width: 150
    },
    {
      title: 'Category',
      dataIndex: ['category', 'name'],
      key: 'category',
      width: 120
    },
    {
      title: 'Price',
      key: 'price',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            ${record.pricing?.basePrice?.toFixed(2)}
          </div>
          {record.pricing?.salePrice && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Sale: ${record.pricing.salePrice.toFixed(2)}
            </Text>
          )}
        </div>
      ),
      width: 100
    },
    {
      title: 'Stock',
      key: 'stock',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {record.inventory?.trackQuantity ? record.inventory.quantity : '∞'}
          </div>
          <Tag color={record.inventory?.stockStatus === 'in_stock' ? 'success' : 'error'} size="small">
            {record.inventory?.stockStatus?.replace('_', ' ')}
          </Tag>
        </div>
      ),
      width: 100
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <div>
          <Tag color={getStatusColor(record.status)}>
            {record.status?.replace('_', ' ')}
          </Tag>
          {record.approval?.status && (
            <Tag color={getApprovalStatusColor(record.approval.status)} size="small">
              {record.approval.status?.replace('_', ' ')}
            </Tag>
          )}
        </div>
      ),
      width: 120
    },
    {
      title: 'Rating',
      key: 'rating',
      render: (_, record) => (
        <div style={{ textAlign: 'center' }}>
          <Rate disabled value={record.reviews?.averageRating || 0} style={{ fontSize: '12px' }} />
          <div style={{ fontSize: '11px', color: '#666' }}>
            ({record.reviews?.totalReviews || 0})
          </div>
        </div>
      ),
      width: 100
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>Products Management</Title>
        <Text type="secondary">
          Manage all products, approvals, and inventory across your platform
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Products"
              value={stats.totalProducts || 0}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Pending Approval"
              value={pendingProducts.length}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Active Products"
              value={stats.activeProducts || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Low Stock"
              value={stats.lowStockProducts || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <ShoppingOutlined />
                All Products
              </span>
            } 
            key="all"
          >
            {/* Filters and Actions */}
            <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space>
                <Select
                  placeholder="Filter by status"
                  style={{ width: 150 }}
                  value={filters.status}
                  onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
                  allowClear
                >
                  <Option value="draft">Draft</Option>
                  <Option value="pending_approval">Pending Approval</Option>
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                  <Option value="rejected">Rejected</Option>
                  <Option value="archived">Archived</Option>
                </Select>
                
                <Input.Search
                  placeholder="Search products..."
                  style={{ width: 250 }}
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  onSearch={fetchProducts}
                />
              </Space>
              
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchProducts}
                loading={loading}
              >
                Refresh
              </Button>
            </div>

            <Table
              columns={[
                ...productColumns,
                {
                  title: 'Actions',
                  key: 'actions',
                  render: (_, record) => (
                    <Space direction="vertical" size="small">
                      <Space>
                        <Tooltip title="View Details">
                          <Button
                            type="primary"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => {/* TODO: Implement view details */}}
                          />
                        </Tooltip>
                        
                        <Tooltip title={record.featured ? "Remove from Featured" : "Add to Featured"}>
                          <Button
                            size="small"
                            icon={<StarOutlined />}
                            style={{ 
                              color: record.featured ? '#faad14' : '#d9d9d9',
                              borderColor: record.featured ? '#faad14' : '#d9d9d9'
                            }}
                            onClick={() => handleToggleFeatured(record._id, record.featured)}
                          />
                        </Tooltip>
                      </Space>
                      
                      <Space>
                        <Select
                          size="small"
                          value={record.status}
                          style={{ width: 120 }}
                          onChange={(value) => handleStatusChange(record._id, value)}
                        >
                          <Option value="draft">Draft</Option>
                          <Option value="active">Active</Option>
                          <Option value="inactive">Inactive</Option>
                          <Option value="archived">Archived</Option>
                        </Select>
                      </Space>
                    </Space>
                  ),
                  width: 150,
                  fixed: 'right'
                }
              ]}
              dataSource={products}
              rowKey="_id"
              loading={loading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} of ${total} products`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize
                  }));
                }
              }}
              scroll={{ x: 1200 }}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                Pending Approval
                {pendingProducts.length > 0 && (
                  <Badge count={pendingProducts.length} style={{ marginLeft: '8px' }} />
                )}
              </span>
            }
            key="pending"
          >
            <Table
              columns={[
                ...productColumns,
                {
                  title: 'Submitted',
                  key: 'submitted',
                  render: (_, record) => (
                    <div>
                      <div style={{ fontSize: '12px' }}>
                        {new Date(record.approval?.submittedAt).toLocaleDateString()}
                      </div>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {new Date(record.approval?.submittedAt).toLocaleTimeString()}
                      </Text>
                    </div>
                  ),
                  width: 100
                },
                {
                  title: 'Actions',
                  key: 'actions',
                  render: (_, record) => (
                    <Space direction="vertical" size="small">
                      <Space>
                        <Button
                          type="primary"
                          size="small"
                          icon={<CheckCircleOutlined />}
                          onClick={() => handleApprovalAction(record, 'approve')}
                        >
                          Approve
                        </Button>
                        <Button
                          danger
                          size="small"
                          icon={<CloseCircleOutlined />}
                          onClick={() => handleApprovalAction(record, 'reject')}
                        >
                          Reject
                        </Button>
                      </Space>
                      <Button
                        size="small"
                        icon={<FileTextOutlined />}
                        onClick={() => handleApprovalAction(record, 'request_changes')}
                        style={{ width: '100%' }}
                      >
                        Request Changes
                      </Button>
                    </Space>
                  ),
                  width: 150,
                  fixed: 'right'
                }
              ]}
              dataSource={pendingProducts}
              rowKey="_id"
              loading={loading}
              pagination={false}
              scroll={{ x: 1200 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Approval Modal */}
      <Modal
        title={`${approvalAction?.charAt(0).toUpperCase() + approvalAction?.slice(1).replace('_', ' ')} Product`}
        open={approvalModalVisible}
        onCancel={() => {
          setApprovalModalVisible(false);
          approvalForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {selectedProduct && (
          <div>
            <div style={{ marginBottom: '16px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <Image
                  width={60}
                  height={60}
                  src={selectedProduct.images?.[0]?.url || '/placeholder-product.png'}
                  alt={selectedProduct.name}
                  style={{ borderRadius: '4px', objectFit: 'cover' }}
                  fallback="/placeholder-product.png"
                />
                <div>
                  <div style={{ fontWeight: 500, fontSize: '16px' }}>
                    {selectedProduct.name}
                  </div>
                  <Text type="secondary">
                    by {selectedProduct.vendor?.businessName}
                  </Text>
                  <div style={{ marginTop: '4px' }}>
                    <Tag color="blue" size="small">
                      {selectedProduct.category?.name}
                    </Tag>
                    <Text type="secondary" style={{ fontSize: '12px', marginLeft: '8px' }}>
                      SKU: {selectedProduct.sku}
                    </Text>
                  </div>
                </div>
              </div>
            </div>

            <Form
              form={approvalForm}
              layout="vertical"
              onFinish={handleApprovalSubmit}
            >
              {approvalAction === 'reject' && (
                <Form.Item
                  name="reason"
                  label="Rejection Reason"
                  rules={[{ required: true, message: 'Please provide a rejection reason' }]}
                >
                  <Select placeholder="Select rejection reason">
                    <Option value="incomplete_information">Incomplete Information</Option>
                    <Option value="poor_quality_images">Poor Quality Images</Option>
                    <Option value="inappropriate_content">Inappropriate Content</Option>
                    <Option value="pricing_issues">Pricing Issues</Option>
                    <Option value="category_mismatch">Category Mismatch</Option>
                    <Option value="duplicate_product">Duplicate Product</Option>
                    <Option value="policy_violation">Policy Violation</Option>
                    <Option value="other">Other</Option>
                  </Select>
                </Form.Item>
              )}

              {approvalAction === 'request_changes' && (
                <Form.Item
                  name="changes"
                  label="Required Changes"
                  rules={[{ required: true, message: 'Please specify required changes' }]}
                >
                  <Select
                    mode="tags"
                    placeholder="Add change requests"
                    style={{ width: '100%' }}
                  >
                    <Option value="Update product description">Update product description</Option>
                    <Option value="Add more product images">Add more product images</Option>
                    <Option value="Correct pricing information">Correct pricing information</Option>
                    <Option value="Update category selection">Update category selection</Option>
                    <Option value="Add product specifications">Add product specifications</Option>
                    <Option value="Improve image quality">Improve image quality</Option>
                  </Select>
                </Form.Item>
              )}

              <Form.Item
                name="notes"
                label="Additional Notes"
              >
                <TextArea
                  rows={4}
                  placeholder={`Add any additional notes for the ${approvalAction}...`}
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setApprovalModalVisible(false)}>
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    danger={approvalAction === 'reject'}
                  >
                    {approvalAction === 'approve' && 'Approve Product'}
                    {approvalAction === 'reject' && 'Reject Product'}
                    {approvalAction === 'request_changes' && 'Request Changes'}
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProductsManagement;
