import React from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import {
  ShopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DollarOutlined
} from '@ant-design/icons';

const VendorStatistics = ({ vendors, stats, loading }) => {
  // Use stats from API if available, fallback to calculated values
  const totalVendors = stats.totalVendors || vendors.length;
  const activeVendors = stats.activeVendors || vendors.filter(vendor => vendor.status === 'active').length;
  const pendingVendorsCount = stats.pendingVendors || vendors.filter(vendor => vendor.status === 'pending_approval').length;
  const totalSales = stats.totalRevenue || vendors.reduce((sum, vendor) => sum + (vendor.performance?.totalRevenue || 0), 0);

  return (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Vendors"
            value={totalVendors}
            prefix={<ShopOutlined />}
            valueStyle={{ color: '#1890ff' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Active"
            value={activeVendors}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Pending Approval"
            value={pendingVendorsCount}
            prefix={<CloseCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Sales"
            value={totalSales}
            prefix={<DollarOutlined />}
            formatter={(value) => `$${value.toLocaleString()}`}
            valueStyle={{ color: '#52c41a' }}
            loading={loading}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default VendorStatistics;
