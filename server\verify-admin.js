#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./schema/userSchema');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
  reset: '\x1b[0m'
};

async function verifyAdmin() {
  try {
    // Connect to database
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
    await mongoose.connect(uri);
    console.log(`${colors.green}✅ Connected to MongoDB${colors.reset}\n`);

    // Find admin account with password field
    const admin = await User.findOne({ email: '<EMAIL>' }).select('+password');
    
    if (!admin) {
      console.log(`${colors.red}❌ Admin account not found!${colors.reset}`);
      console.log(`${colors.yellow}Run: node create-admin-account.js${colors.reset}`);
      return;
    }

    console.log(`${colors.bold}${colors.blue}🔍 Admin Account Verification${colors.reset}`);
    console.log(`${colors.bold}=============================${colors.reset}`);

    // Check all critical fields
    const checks = [
      { name: 'Email', value: admin.email, expected: '<EMAIL>', pass: admin.email === '<EMAIL>' },
      { name: 'User Type', value: admin.userType, expected: 'admin', pass: admin.userType === 'admin' },
      { name: 'Role', value: admin.role, expected: 'super_admin', pass: admin.role === 'super_admin' },
      { name: 'Active Status', value: admin.isActive, expected: true, pass: admin.isActive === true },
      { name: 'Blocked Status', value: admin.isBlocked, expected: false, pass: admin.isBlocked === false },
      { name: 'Email Verified', value: admin.emailVerification.isVerified, expected: true, pass: admin.emailVerification.isVerified === true }
    ];

    let allPassed = true;
    checks.forEach(check => {
      const status = check.pass ? `${colors.green}✅ PASS${colors.reset}` : `${colors.red}❌ FAIL${colors.reset}`;
      console.log(`${colors.bold}${check.name}:${colors.reset} ${check.value} ${status}`);
      if (!check.pass) allPassed = false;
    });

    console.log(`${colors.bold}=============================${colors.reset}`);

    if (allPassed) {
      console.log(`${colors.bold}${colors.green}🎉 Admin account is properly configured!${colors.reset}`);
      console.log(`${colors.bold}${colors.cyan}Ready for admin panel access${colors.reset}\n`);
      
      // Test password
      const passwordValid = await admin.comparePassword('password@admin123');
      console.log(`${colors.bold}Password Test:${colors.reset} ${passwordValid ? colors.green + '✅ Valid' : colors.red + '❌ Invalid'}${colors.reset}\n`);
      
      console.log(`${colors.bold}${colors.green}📱 Login Credentials:${colors.reset}`);
      console.log(`${colors.cyan}Email: <EMAIL>${colors.reset}`);
      console.log(`${colors.cyan}Password: password@admin123${colors.reset}\n`);
      
      console.log(`${colors.bold}${colors.blue}🌐 Admin Panel URLs:${colors.reset}`);
      console.log(`${colors.yellow}Local Frontend: http://localhost:5173/admin${colors.reset}`);
      console.log(`${colors.yellow}API Endpoint: http://localhost:5000/api/admin${colors.reset}`);
      
    } else {
      console.log(`${colors.bold}${colors.red}❌ Admin account configuration issues detected!${colors.reset}`);
      console.log(`${colors.yellow}Run the create-admin-account.js script again to fix issues.${colors.reset}`);
    }

  } catch (error) {
    console.error(`${colors.red}❌ Verification failed: ${error.message}${colors.reset}`);
  } finally {
    await mongoose.disconnect();
    console.log(`${colors.yellow}📴 Database connection closed${colors.reset}`);
  }
}

// Run verification
verifyAdmin().catch(console.error);
