import React, { useState, useEffect, useRef } from "react";
import { SearchOutlined, DownOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useSearch } from "../contexts/SearchContext";

const MobileSearchBar = ({ closeAllModals }) => {
    const [mobileSearchInput, setMobileSearchInput] = useState('');
    const [isProductDropdownOpen, setIsProductDropdownOpen] = useState(false);
    const mobileSearchInputRef = useRef(null);
    const navigate = useNavigate();
    const { 
        suggestions, 
        showSuggestions, 
        setShowSuggestions, 
        getSuggestions,
        setSearchTerm
    } = useSearch();

    const productCategories = [
        "Electronics",
        "Fashion",
        "Home & Garden",
        "Sports",
        "Books",
        "Toys",
        "Beauty",
        "Automotive"
    ];

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (mobileSearchInputRef.current && !mobileSearchInputRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleSearchInput = (value) => {
        setMobileSearchInput(value);
        if (value.trim().length >= 2) {
            getSuggestions(value);
            setShowSuggestions(true);
        } else {
            setShowSuggestions(false);
        }
    };

    const handleSearch = () => {
        if (mobileSearchInput.trim()) {
            setSearchTerm(mobileSearchInput.trim());
            navigate(`/search?q=${encodeURIComponent(mobileSearchInput.trim())}`);
            setShowSuggestions(false);
            closeAllModals();
        }
    };

    const handleSuggestionClick = (suggestion) => {
        const searchValue = suggestion.title || suggestion.name || suggestion;
        setMobileSearchInput(searchValue);
        setSearchTerm(searchValue);
        navigate(`/search?q=${encodeURIComponent(searchValue)}`);
        setShowSuggestions(false);
        closeAllModals();
    };

    return (
        <div className="lg:hidden px-2 pb-3">
            <div className="relative w-full">
                <div className="flex">
                    <div className="relative">
                        <button 
                            className="flex items-center px-3 border border-r-0 border-gray-600 rounded-l-md bg-gray-800 text-white h-10 hover:bg-gray-700 text-sm active:bg-gray-600 transition-colors touch-manipulation"
                            onClick={() => setIsProductDropdownOpen(!isProductDropdownOpen)}
                            style={{ minHeight: '44px' }}
                        >
                            All <DownOutlined className="ml-1 text-xs" />
                        </button>
                        
                        {/* Mobile Products Dropdown */}
                        {isProductDropdownOpen && (
                            <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                                {productCategories.map((category, index) => (
                                    <a
                                        key={index}
                                        href="#"
                                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 active:bg-gray-200 touch-manipulation"
                                        onClick={() => setIsProductDropdownOpen(false)}
                                    >
                                        {category}
                                    </a>
                                ))}
                            </div>
                        )}
                    </div>
                    <div className="relative flex-1" ref={mobileSearchInputRef}>
                        <input
                            type="text"
                            placeholder="Search products..."
                            className="w-full py-2 px-3 border border-gray-600 focus:outline-none h-10 bg-white text-black text-sm"
                            style={{ minHeight: '44px' }}
                            value={mobileSearchInput}
                            onChange={(e) => handleSearchInput(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                        />
                        
                        {/* Mobile Search Suggestions Dropdown */}
                        {showSuggestions && suggestions.length > 0 && (
                            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                                {suggestions.slice(0, 8).map((suggestion, index) => (
                                    <button
                                        key={index}
                                        onClick={() => handleSuggestionClick(suggestion)}
                                        className="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 active:bg-gray-200 flex items-center touch-manipulation"
                                        style={{ minHeight: '44px' }}
                                    >
                                        <SearchOutlined className="mr-3 text-gray-400" />
                                        <span>{suggestion.title || suggestion.name || suggestion}</span>
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                    <button 
                        className="flex items-center justify-center px-3 bg-orange-500 text-white rounded-r-md h-10 hover:bg-orange-600 active:bg-orange-700 transition-colors touch-manipulation"
                        style={{ minHeight: '44px' }}
                        onClick={handleSearch}
                    >
                        <SearchOutlined />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default MobileSearchBar;
