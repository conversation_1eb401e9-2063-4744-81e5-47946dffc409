const HomepageSettings = require('../../models/HomepageSettings');
const Category = require('../../models/Category');
const { deleteImage, extractPublicId } = require('../../config/cloudinary');

// Get homepage settings
const getHomepageSettings = async (req, res) => {
  try {
    // Get settings first, then populate if needed
    let settings = await HomepageSettings.getSettings();

    // If settings has featuredCategories, populate them
    if (settings && settings.featuredCategories && settings.featuredCategories.length > 0) {
      settings = await HomepageSettings.findById(settings._id)
        .populate('featuredCategories.category', 'name slug');
    }

    res.status(200).json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching homepage settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch homepage settings',
      error: error.message
    });
  }
};

// Update general settings
const updateGeneralSettings = async (req, res) => {
  try {
    const { autoPlayCarousel, carouselSpeed, showPromotions, maxCarouselImages, maxPromotionImages } = req.body;
    
    const settings = await HomepageSettings.getSettings();
    
    if (autoPlayCarousel !== undefined) settings.settings.autoPlayCarousel = autoPlayCarousel;
    if (carouselSpeed !== undefined) settings.settings.carouselSpeed = carouselSpeed;
    if (showPromotions !== undefined) settings.settings.showPromotions = showPromotions;
    if (maxCarouselImages !== undefined) settings.settings.maxCarouselImages = maxCarouselImages;
    if (maxPromotionImages !== undefined) settings.settings.maxPromotionImages = maxPromotionImages;
    
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error.message
    });
  }
};

// Add carousel image
const addCarouselImage = async (req, res) => {
  try {
    const { title, description, linkUrl } = req.body;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Image file is required'
      });
    }
    
    const settings = await HomepageSettings.getSettings();
    
    // Check if we've reached the maximum number of carousel images
    if (settings.carouselImages.length >= settings.settings.maxCarouselImages) {
      return res.status(400).json({
        success: false,
        message: `Maximum ${settings.settings.maxCarouselImages} carousel images allowed`
      });
    }
    
    const imageData = {
      title,
      description,
      imageUrl: req.file.path,
      cloudinaryPublicId: req.file.filename,
      linkUrl,
      sortOrder: settings.carouselImages.length
    };
    
    await settings.addCarouselImage(imageData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(201).json({
      success: true,
      message: 'Carousel image added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add carousel image',
      error: error.message
    });
  }
};

// Update carousel image
const updateCarouselImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { title, description, linkUrl, isActive, sortOrder } = req.body;
    
    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.carouselImages.findIndex(img => img._id.toString() === imageId);
    
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Carousel image not found'
      });
    }
    
    const image = settings.carouselImages[imageIndex];
    
    // Update image file if provided
    if (req.file) {
      // Delete old image from Cloudinary
      if (image.cloudinaryPublicId) {
        await deleteImage(image.cloudinaryPublicId);
      }
      
      image.imageUrl = req.file.path;
      image.cloudinaryPublicId = req.file.filename;
    }
    
    // Update other fields
    if (title !== undefined) image.title = title;
    if (description !== undefined) image.description = description;
    if (linkUrl !== undefined) image.linkUrl = linkUrl;
    if (isActive !== undefined) image.isActive = isActive;
    if (sortOrder !== undefined) image.sortOrder = sortOrder;
    
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(200).json({
      success: true,
      message: 'Carousel image updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update carousel image',
      error: error.message
    });
  }
};

// Delete carousel image
const deleteCarouselImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    
    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.carouselImages.findIndex(img => img._id.toString() === imageId);
    
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Carousel image not found'
      });
    }
    
    const image = settings.carouselImages[imageIndex];
    
    // Delete image from Cloudinary
    if (image.cloudinaryPublicId) {
      await deleteImage(image.cloudinaryPublicId);
    }
    
    // Remove image from array
    settings.carouselImages.splice(imageIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(200).json({
      success: true,
      message: 'Carousel image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting carousel image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete carousel image',
      error: error.message
    });
  }
};

// Add promotion image
const addPromotionImage = async (req, res) => {
  try {
    const { title, description, linkUrl, position, startDate, endDate } = req.body;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Image file is required'
      });
    }
    
    const settings = await HomepageSettings.getSettings();
    
    // Check if we've reached the maximum number of promotion images
    if (settings.promotionImages.length >= settings.settings.maxPromotionImages) {
      return res.status(400).json({
        success: false,
        message: `Maximum ${settings.settings.maxPromotionImages} promotion images allowed`
      });
    }
    
    const imageData = {
      title,
      description,
      imageUrl: req.file.path,
      cloudinaryPublicId: req.file.filename,
      linkUrl,
      position: position || 'sidebar',
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      sortOrder: settings.promotionImages.length
    };
    
    await settings.addPromotionImage(imageData);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(201).json({
      success: true,
      message: 'Promotion image added successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error adding promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add promotion image',
      error: error.message
    });
  }
};

// Update promotion image
const updatePromotionImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    const { title, description, linkUrl, position, isActive, sortOrder, startDate, endDate } = req.body;
    
    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.promotionImages.findIndex(img => img._id.toString() === imageId);
    
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Promotion image not found'
      });
    }
    
    const image = settings.promotionImages[imageIndex];
    
    // Update image file if provided
    if (req.file) {
      // Delete old image from Cloudinary
      if (image.cloudinaryPublicId) {
        await deleteImage(image.cloudinaryPublicId);
      }
      
      image.imageUrl = req.file.path;
      image.cloudinaryPublicId = req.file.filename;
    }
    
    // Update other fields
    if (title !== undefined) image.title = title;
    if (description !== undefined) image.description = description;
    if (linkUrl !== undefined) image.linkUrl = linkUrl;
    if (position !== undefined) image.position = position;
    if (isActive !== undefined) image.isActive = isActive;
    if (sortOrder !== undefined) image.sortOrder = sortOrder;
    if (startDate !== undefined) image.startDate = startDate ? new Date(startDate) : undefined;
    if (endDate !== undefined) image.endDate = endDate ? new Date(endDate) : undefined;
    
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(200).json({
      success: true,
      message: 'Promotion image updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update promotion image',
      error: error.message
    });
  }
};

// Delete promotion image
const deletePromotionImage = async (req, res) => {
  try {
    const { imageId } = req.params;
    
    const settings = await HomepageSettings.getSettings();
    const imageIndex = settings.promotionImages.findIndex(img => img._id.toString() === imageId);
    
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Promotion image not found'
      });
    }
    
    const image = settings.promotionImages[imageIndex];
    
    // Delete image from Cloudinary
    if (image.cloudinaryPublicId) {
      await deleteImage(image.cloudinaryPublicId);
    }
    
    // Remove image from array
    settings.promotionImages.splice(imageIndex, 1);
    settings.lastUpdatedBy = req.user.id;
    await settings.save();
    
    res.status(200).json({
      success: true,
      message: 'Promotion image deleted successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error deleting promotion image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete promotion image',
      error: error.message
    });
  }
};

module.exports = {
  getHomepageSettings,
  updateGeneralSettings,
  addCarouselImage,
  updateCarouselImage,
  deleteCarouselImage,
  addPromotionImage,
  updatePromotionImage,
  deletePromotionImage
};
