import React, { useState, useEffect } from 'react';
import {
  Tabs, Card, Button, Form, Input, message, Upload, Modal,
  Table, Switch, InputNumber, Select, DatePicker, Space, Popconfirm,
  Image, Tag, Tooltip
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined,
  PictureOutlined, SettingOutlined, BankOutlined
} from '@ant-design/icons';
import { homepageSettingsApi } from '../../../services/adminApi';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const HomepageSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('carousel');

  // Modal states
  const [carouselModalVisible, setCarouselModalVisible] = useState(false);
  const [promotionModalVisible, setPromotionModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // Form states
  const [carouselForm] = Form.useForm();
  const [promotionForm] = Form.useForm();
  const [settingsForm] = Form.useForm();

  // Edit states
  const [editingCarousel, setEditingCarousel] = useState(null);
  const [editingPromotion, setEditingPromotion] = useState(null);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await homepageSettingsApi.getSettings();
      if (response.data.success) {
        setSettings(response.data.data);
        // Initialize settings form with current values
        settingsForm.setFieldsValue({
          autoPlayCarousel: response.data.data.settings?.autoPlayCarousel,
          carouselSpeed: response.data.data.settings?.carouselSpeed,
          showPromotions: response.data.data.settings?.showPromotions,
          maxCarouselImages: response.data.data.settings?.maxCarouselImages,
          maxPromotionImages: response.data.data.settings?.maxPromotionImages
        });
      }
    } catch (error) {
      message.error('Failed to fetch homepage settings');
    } finally {
      setLoading(false);
    }
  };

  // Carousel handlers
  const handleAddCarousel = () => {
    setEditingCarousel(null);
    carouselForm.resetFields();
    setCarouselModalVisible(true);
  };

  const handleEditCarousel = (carousel) => {
    setEditingCarousel(carousel);
    carouselForm.setFieldsValue({
      title: carousel.title,
      description: carousel.description,
      linkUrl: carousel.linkUrl,
      isActive: carousel.isActive,
      sortOrder: carousel.sortOrder
    });
    setCarouselModalVisible(true);
  };

  const handleDeleteCarousel = async (imageId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deleteCarouselImage(imageId);
      message.success('Carousel image deleted successfully');
      fetchSettings();
    } catch (error) {
      message.error('Failed to delete carousel image');
    } finally {
      setLoading(false);
    }
  };

  const handleCarouselSubmit = async () => {
    try {
      const values = await carouselForm.validateFields();
      setLoading(true);

      const formData = new FormData();
      formData.append('title', values.title || '');
      formData.append('description', values.description || '');
      formData.append('linkUrl', values.linkUrl || '');

      if (values.image && values.image.length > 0) {
        formData.append('image', values.image[0].originFileObj);
      }

      if (editingCarousel) {
        formData.append('isActive', values.isActive);
        formData.append('sortOrder', values.sortOrder);
        await homepageSettingsApi.updateCarouselImage(editingCarousel._id, formData);
        message.success('Carousel image updated successfully');
      } else {
        await homepageSettingsApi.addCarouselImage(formData);
        message.success('Carousel image added successfully');
      }

      setCarouselModalVisible(false);
      fetchSettings();
    } catch (error) {
      message.error('Failed to save carousel image');
    } finally {
      setLoading(false);
    }
  };

  // Promotion handlers
  const handleAddPromotion = () => {
    setEditingPromotion(null);
    promotionForm.resetFields();
    setPromotionModalVisible(true);
  };

  const handleEditPromotion = (promotion) => {
    setEditingPromotion(promotion);
    promotionForm.setFieldsValue({
      title: promotion.title,
      description: promotion.description,
      linkUrl: promotion.linkUrl,
      position: promotion.position,
      isActive: promotion.isActive,
      sortOrder: promotion.sortOrder,
      dateRange: promotion.startDate && promotion.endDate ?
        [dayjs(promotion.startDate), dayjs(promotion.endDate)] : null
    });
    setPromotionModalVisible(true);
  };

  const handleDeletePromotion = async (imageId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deletePromotionImage(imageId);
      message.success('Promotion image deleted successfully');
      fetchSettings();
    } catch (error) {
      message.error('Failed to delete promotion image');
    } finally {
      setLoading(false);
    }
  };

  const handlePromotionSubmit = async () => {
    try {
      const values = await promotionForm.validateFields();
      setLoading(true);

      const formData = new FormData();
      formData.append('title', values.title || '');
      formData.append('description', values.description || '');
      formData.append('linkUrl', values.linkUrl || '');
      formData.append('position', values.position || 'sidebar');

      if (values.dateRange && values.dateRange.length === 2) {
        formData.append('startDate', values.dateRange[0].toISOString());
        formData.append('endDate', values.dateRange[1].toISOString());
      }

      if (values.image && values.image.length > 0) {
        formData.append('image', values.image[0].originFileObj);
      }

      if (editingPromotion) {
        formData.append('isActive', values.isActive);
        formData.append('sortOrder', values.sortOrder);
        await homepageSettingsApi.updatePromotionImage(editingPromotion._id, formData);
        message.success('Promotion image updated successfully');
      } else {
        await homepageSettingsApi.addPromotionImage(formData);
        message.success('Promotion image added successfully');
      }

      setPromotionModalVisible(false);
      fetchSettings();
    } catch (error) {
      message.error('Failed to save promotion image');
    } finally {
      setLoading(false);
    }
  };

  // Settings handlers
  const handleSettingsSubmit = async () => {
    try {
      const values = await settingsForm.validateFields();
      setLoading(true);

      await homepageSettingsApi.updateGeneralSettings(values);
      message.success('Settings updated successfully');
      setSettingsModalVisible(false);
      fetchSettings();
    } catch (error) {
      message.error('Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  // Table columns for carousel images
  const carouselColumns = [
    {
      title: 'Image',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl) => (
        <Image
          width={60}
          height={40}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title) => title || '-',
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Sort Order',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 100,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditCarousel(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this carousel image?"
            onConfirm={() => handleDeleteCarousel(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Table columns for promotion images
  const promotionColumns = [
    {
      title: 'Image',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl) => (
        <Image
          width={60}
          height={40}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title) => title || '-',
    },
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position',
      render: (position) => (
        <Tag color="blue">{position}</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => {
        if (record.startDate && record.endDate) {
          return (
            <div>
              <div>{dayjs(record.startDate).format('MMM DD, YYYY')}</div>
              <div>{dayjs(record.endDate).format('MMM DD, YYYY')}</div>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPromotion(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this promotion image?"
            onConfirm={() => handleDeletePromotion(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  if (!settings) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Button
              type="primary"
              icon={<SettingOutlined />}
              onClick={() => setSettingsModalVisible(true)}
            >
              General Settings
            </Button>
          }
        >
          <TabPane tab={<span><PictureOutlined />Carousel Images</span>} key="carousel">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddCarousel}
                disabled={settings.carouselImages?.length >= (settings.settings?.maxCarouselImages || 10)}
              >
                Add Carousel Image
              </Button>
              {settings.carouselImages?.length >= (settings.settings?.maxCarouselImages || 10) && (
                <span style={{ marginLeft: 8, color: '#ff4d4f' }}>
                  Maximum {settings.settings?.maxCarouselImages || 10} images allowed
                </span>
              )}
            </div>
            <Table
              columns={carouselColumns}
              dataSource={settings.carouselImages || []}
              rowKey="_id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab={<span><BankOutlined />Promotion Images</span>} key="promotions">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddPromotion}
                disabled={settings.promotionImages?.length >= (settings.settings?.maxPromotionImages || 5)}
              >
                Add Promotion Image
              </Button>
              {settings.promotionImages?.length >= (settings.settings?.maxPromotionImages || 5) && (
                <span style={{ marginLeft: 8, color: '#ff4d4f' }}>
                  Maximum {settings.settings?.maxPromotionImages || 5} images allowed
                </span>
              )}
            </div>
            <Table
              columns={promotionColumns}
              dataSource={settings.promotionImages || []}
              rowKey="_id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Carousel Modal */}
      <Modal
        title={editingCarousel ? 'Edit Carousel Image' : 'Add Carousel Image'}
        open={carouselModalVisible}
        onOk={handleCarouselSubmit}
        onCancel={() => setCarouselModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form form={carouselForm} layout="vertical">
          <Form.Item
            name="image"
            label="Image"
            rules={editingCarousel ? [] : [{ required: true, message: 'Please upload an image' }]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item name="title" label="Title">
            <Input placeholder="Enter carousel title" />
          </Form.Item>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter carousel description" />
          </Form.Item>

          <Form.Item name="linkUrl" label="Link URL">
            <Input placeholder="Enter link URL (optional)" />
          </Form.Item>

          {editingCarousel && (
            <>
              <Form.Item name="isActive" label="Active" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item name="sortOrder" label="Sort Order">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Promotion Modal */}
      <Modal
        title={editingPromotion ? 'Edit Promotion Image' : 'Add Promotion Image'}
        open={promotionModalVisible}
        onOk={handlePromotionSubmit}
        onCancel={() => setPromotionModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form form={promotionForm} layout="vertical">
          <Form.Item
            name="image"
            label="Image"
            rules={editingPromotion ? [] : [{ required: true, message: 'Please upload an image' }]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item name="title" label="Title">
            <Input placeholder="Enter promotion title" />
          </Form.Item>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter promotion description" />
          </Form.Item>

          <Form.Item name="linkUrl" label="Link URL">
            <Input placeholder="Enter link URL (optional)" />
          </Form.Item>

          <Form.Item name="position" label="Position" rules={[{ required: true }]}>
            <Select placeholder="Select position">
              <Option value="sidebar">Sidebar</Option>
              <Option value="banner">Banner</Option>
              <Option value="popup">Popup</Option>
              <Option value="footer">Footer</Option>
            </Select>
          </Form.Item>

          <Form.Item name="dateRange" label="Duration (Optional)">
            <RangePicker
              style={{ width: '100%' }}
              placeholder={['Start Date', 'End Date']}
            />
          </Form.Item>

          {editingPromotion && (
            <>
              <Form.Item name="isActive" label="Active" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item name="sortOrder" label="Sort Order">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Settings Modal */}
      <Modal
        title="General Settings"
        open={settingsModalVisible}
        onOk={handleSettingsSubmit}
        onCancel={() => setSettingsModalVisible(false)}
        confirmLoading={loading}
        width={500}
      >
        <Form form={settingsForm} layout="vertical">
          <Form.Item name="autoPlayCarousel" label="Auto Play Carousel" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="carouselSpeed" label="Carousel Speed (ms)">
            <InputNumber min={1000} max={10000} step={500} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="showPromotions" label="Show Promotions" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="maxCarouselImages" label="Max Carousel Images">
            <InputNumber min={1} max={20} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="maxPromotionImages" label="Max Promotion Images">
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default HomepageSettings;

