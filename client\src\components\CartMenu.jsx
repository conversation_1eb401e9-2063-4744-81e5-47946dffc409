import React, { useState } from "react";
import { ShoppingCartOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useCart } from "../contexts/CartContext";

const CartMenu = ({ isMobile = false, onMenuClose }) => {
    const [isCartModalOpen, setIsCartModalOpen] = useState(false);
    const navigate = useNavigate();
    const { summary } = useCart();

    const handleCartClick = () => {
        navigate('/cart');
        setIsCartModalOpen(false);
        onMenuClose?.();
    };

    const buttonClasses = isMobile 
        ? "relative text-white hover:text-orange-500 text-lg p-2 rounded-md active:bg-gray-700 transition-colors touch-manipulation"
        : "relative text-white hover:text-orange-500 text-lg";

    const buttonStyle = isMobile ? { minHeight: '44px', minWidth: '44px' } : {};

    const viewCartButtonClasses = isMobile 
        ? "w-full bg-orange-500 text-white py-3 px-4 rounded hover:bg-orange-600 active:bg-orange-700 transition-colors touch-manipulation"
        : "w-full bg-orange-500 text-white py-2 px-4 rounded hover:bg-orange-600";

    const viewCartButtonStyle = isMobile ? { minHeight: '44px' } : {};

    return (
        <div className="relative">
            <button 
                onClick={() => {
                    setIsCartModalOpen(!isCartModalOpen);
                    if (isMobile && onMenuClose) {
                        onMenuClose();
                    }
                }}
                className={buttonClasses}
                style={buttonStyle}
            >
                <ShoppingCartOutlined />
                {summary?.totalItems > 0 && (
                    <span className={`absolute bg-orange-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium ${isMobile ? '-top-1 -right-1' : '-top-2 -right-2'}`}>
                        {summary.totalItems > 99 ? '99+' : summary.totalItems}
                    </span>
                )}
            </button>
        
            {/* Cart Modal */}
            {isCartModalOpen && (
                <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50">
                    <div className="p-4">
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">Shopping Cart</h3>
                        {summary?.totalItems > 0 ? (
                            <div className="space-y-2 mb-4">
                                <p className="text-sm text-gray-600">
                                    {summary.totalItems} item{summary.totalItems > 1 ? 's' : ''}
                                </p>
                                <p className="text-lg font-semibold text-gray-900">
                                    ${summary.totalAmount.toFixed(2)}
                                </p>
                            </div>
                        ) : (
                            <p className="text-gray-600 text-sm mb-4">Your cart is empty</p>
                        )}
                        <button 
                            onClick={handleCartClick}
                            className={viewCartButtonClasses}
                            style={viewCartButtonStyle}
                        >
                            View Cart
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CartMenu;
