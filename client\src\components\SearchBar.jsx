import React, { useState, useEffect, useRef } from "react";
import { SearchOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useSearch } from "../contexts/SearchContext";

const SearchBar = ({ closeAllModals }) => {
    const [searchInput, setSearchInput] = useState('');
    const searchInputRef = useRef(null);
    const navigate = useNavigate();
    const { 
        suggestions, 
        showSuggestions, 
        setShowSuggestions, 
        getSuggestions,
        setSearchTerm
    } = useSearch();

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (searchInputRef.current && !searchInputRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleSearchInput = (value) => {
        setSearchInput(value);
        if (value.trim().length >= 2) {
            getSuggestions(value);
            setShowSuggestions(true);
        } else {
            setShowSuggestions(false);
        }
    };

    const handleSearch = () => {
        if (searchInput.trim()) {
            setSearchTerm(searchInput.trim());
            navigate(`/search?q=${encodeURIComponent(searchInput.trim())}`);
            setShowSuggestions(false);
            closeAllModals();
        }
    };

    const handleSuggestionClick = (suggestion) => {
        const searchValue = suggestion.title || suggestion.name || suggestion;
        setSearchInput(searchValue);
        setSearchTerm(searchValue);
        navigate(`/search?q=${encodeURIComponent(searchValue)}`);
        setShowSuggestions(false);
        closeAllModals();
    };

    return (
        <div className="flex-1 mx-4 xl:mx-8">
            <div className="relative w-full max-w-3xl">
                <div className="relative flex-1" ref={searchInputRef}>
                    <input
                        type="text"
                        placeholder="What are you looking for..."
                        className="w-full py-2 px-3 xl:px-4 border border-gray-600 focus:outline-none h-10 bg-white text-black text-sm"
                        value={searchInput}
                        onChange={(e) => handleSearchInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    />
                    {showSuggestions && suggestions.length > 0 && (
                        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto">
                            {suggestions.slice(0, 8).map((suggestion, index) => (
                                <button
                                    key={index}
                                    onClick={() => handleSuggestionClick(suggestion)}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                >
                                    <SearchOutlined className="mr-3 text-gray-400" />
                                    <span>{suggestion.title || suggestion.name || suggestion}</span>
                                </button>
                            ))}
                        </div>
                    )}
                </div>
                <button 
                    className="flex items-center justify-center px-3 xl:px-4 bg-orange-500 text-white rounded-r-md h-10 hover:bg-orange-600"
                    onClick={handleSearch}
                >
                    <SearchOutlined />
                    <span className="ml-1 hidden xl:inline text-sm">Search</span>
                </button>
            </div>
        </div>
    );
};

export default SearchBar;
