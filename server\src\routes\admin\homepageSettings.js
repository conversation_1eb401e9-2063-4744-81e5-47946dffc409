const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const imageUpload = require('../../middleware/upload/imageUpload');
const {
  getHomepageSettings,
  updateGeneralSettings,
  addCarouselImage,
  updateCarouselImage,
  deleteCarouselImage,
  addPromotionImage,
  updatePromotionImage,
  deletePromotionImage
} = require('../../controllers/admin/homepageSettingsController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

// General settings routes
router.get('/', getHomepageSettings);
router.put('/settings', updateGeneralSettings);

// Carousel image routes
router.post('/carousel', imageUpload.single('image', 'categoryImages'), addCarouselImage);
router.put('/carousel/:imageId', imageUpload.single('image', 'categoryImages'), updateCarouselImage);
router.delete('/carousel/:imageId', deleteCarouselImage);

// Promotion image routes
router.post('/promotions', imageUpload.single('image', 'categoryImages'), addPromotionImage);
router.put('/promotions/:imageId', imageUpload.single('image', 'categoryImages'), updatePromotionImage);
router.delete('/promotions/:imageId', deletePromotionImage);

module.exports = router;
