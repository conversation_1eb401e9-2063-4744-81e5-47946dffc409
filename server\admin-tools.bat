@echo off
echo.
echo =========================================
echo   Multi-Vendor eCommerce Admin Tools
echo =========================================
echo.
echo 1. Create/Update Admin Account
echo 2. Verify Admin Account
echo 3. Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Creating/Updating admin account...
    node create-admin-account.js
    echo.
    pause
    goto menu
)

if "%choice%"=="2" (
    echo.
    echo Verifying admin account...
    node verify-admin.js
    echo.
    pause
    goto menu
)

if "%choice%"=="3" (
    echo.
    echo Goodbye!
    exit
)

echo Invalid choice. Please try again.
pause
goto menu

:menu
cls
goto start
