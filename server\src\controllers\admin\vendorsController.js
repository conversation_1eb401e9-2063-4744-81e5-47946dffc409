const { Vendor, User, Product, Order } = require('../../models');
const mongoose = require('mongoose');

// Get all vendors with pagination and filtering
const getVendors = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      verificationStatus = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (search) {
      filter.$or = [
        { businessName: { $regex: search, $options: 'i' } },
        { 'contactInfo.businessEmail': { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      filter.status = status;
    }
    
    if (verificationStatus) {
      filter['verification.status'] = verificationStatus;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get vendors with pagination
    const [vendors, totalVendors] = await Promise.all([
      Vendor.find(filter)
        .populate('user', 'firstName lastName email phone createdAt')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Vendor.countDocuments(filter)
    ]);

    // Get additional vendor data
    const vendorsWithStats = await Promise.all(
      vendors.map(async (vendor) => {
        const [productCount, orderStats] = await Promise.all([
          Product.countDocuments({ vendor: vendor._id }),
          Order.aggregate([
            { $unwind: '$items' },
            { $match: { 'items.vendor': vendor._id } },
            {
              $group: {
                _id: null,
                totalOrders: { $sum: 1 },
                totalRevenue: { $sum: '$items.totalPrice' },
                lastOrderDate: { $max: '$createdAt' }
              }
            }
          ])
        ]);

        return {
          ...vendor,
          productCount,
          orderStats: orderStats[0] || {
            totalOrders: 0,
            totalRevenue: 0,
            lastOrderDate: null
          }
        };
      })
    );

    const totalPages = Math.ceil(totalVendors / parseInt(limit));

    res.json({
      success: true,
      data: {
        vendors: vendorsWithStats,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalVendors,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });

  } catch (error) {
    console.error('Get vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendors',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get vendor by ID
const getVendorById = async (req, res) => {
  try {
    const { id } = req.params;

    const vendor = await Vendor.findById(id)
      .populate('user', 'firstName lastName email phone createdAt lastLogin')
      .populate('verification.reviewedBy', 'firstName lastName email');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Get vendor's products
    const products = await Product.find({ vendor: id })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('name sku status pricing.basePrice inventory.quantity sales.totalSold createdAt');

    // Get vendor's recent orders
    const recentOrders = await Order.find({ 'items.vendor': id })
      .populate('customer', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(10)
      .select('orderNumber customer status pricing.total createdAt items');

    // Get vendor statistics
    const [productStats, orderStats, revenueStats] = await Promise.all([
      Product.aggregate([
        { $match: { vendor: vendor._id } },
        {
          $group: {
            _id: null,
            totalProducts: { $sum: 1 },
            activeProducts: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            draftProducts: {
              $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
            },
            totalSold: { $sum: '$sales.totalSold' },
            totalRevenue: { $sum: '$sales.totalRevenue' },
            averagePrice: { $avg: '$pricing.basePrice' }
          }
        }
      ]),
      Order.aggregate([
        { $unwind: '$items' },
        { $match: { 'items.vendor': vendor._id } },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: '$items.totalPrice' },
            averageOrderValue: { $avg: '$items.totalPrice' }
          }
        }
      ]),
      Order.aggregate([
        { $unwind: '$items' },
        { $match: { 'items.vendor': vendor._id } },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            revenue: { $sum: '$items.totalPrice' },
            orders: { $sum: 1 }
          }
        },
        { $sort: { _id: -1 } },
        { $limit: 30 }
      ])
    ]);

    res.json({
      success: true,
      data: {
        vendor,
        recentProducts: products,
        recentOrders,
        statistics: {
          products: productStats[0] || {
            totalProducts: 0,
            activeProducts: 0,
            draftProducts: 0,
            totalSold: 0,
            totalRevenue: 0,
            averagePrice: 0
          },
          orders: orderStats[0] || {
            totalOrders: 0,
            totalRevenue: 0,
            averageOrderValue: 0
          }
        },
        revenueChart: revenueStats
      }
    });

  } catch (error) {
    console.error('Get vendor by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor details',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update vendor
const updateVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated directly
    delete updates._id;
    delete updates.user;
    delete updates.createdAt;
    delete updates.updatedAt;

    const vendor = await Vendor.findByIdAndUpdate(
      id,
      updates,
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      message: 'Vendor updated successfully',
      data: { vendor }
    });

  } catch (error) {
    console.error('Update vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update vendor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update vendor status
const updateVendorStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!['active', 'inactive', 'suspended', 'pending_approval'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status value'
      });
    }

    const vendor = await Vendor.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    ).populate('user', 'firstName lastName email');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // If suspending vendor, also deactivate their products
    if (status === 'suspended' || status === 'inactive') {
      await Product.updateMany(
        { vendor: id },
        { status: 'inactive' }
      );
    }

    res.json({
      success: true,
      message: 'Vendor status updated successfully',
      data: { vendor }
    });

  } catch (error) {
    console.error('Update vendor status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update vendor status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update vendor verification status
const updateVerificationStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, rejectionReason } = req.body;
    const adminId = req.user.id; // Assuming admin is authenticated

    if (!['pending', 'verified', 'rejected', 'suspended'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid verification status'
      });
    }

    const updateData = {
      'verification.status': status,
      'verification.reviewedBy': adminId,
      'verification.verifiedAt': status === 'verified' ? new Date() : undefined
    };

    if (status === 'rejected' && rejectionReason) {
      updateData['verification.rejectionReason'] = rejectionReason;
    }

    const vendor = await Vendor.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('user', 'firstName lastName email')
     .populate('verification.reviewedBy', 'firstName lastName email');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Update vendor status based on verification
    if (status === 'verified') {
      vendor.status = 'active';
      await vendor.save();
    } else if (status === 'rejected' || status === 'suspended') {
      vendor.status = 'inactive';
      await vendor.save();
    }

    res.json({
      success: true,
      message: 'Vendor verification status updated successfully',
      data: { vendor }
    });

  } catch (error) {
    console.error('Update verification status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update verification status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update vendor commission
const updateCommission = async (req, res) => {
  try {
    const { id } = req.params;
    const { rate, type } = req.body;

    if (type && !['percentage', 'fixed'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid commission type'
      });
    }

    if (rate !== undefined && (rate < 0 || rate > 100)) {
      return res.status(400).json({
        success: false,
        message: 'Commission rate must be between 0 and 100'
      });
    }

    const updateData = {};
    if (rate !== undefined) updateData['commission.rate'] = rate;
    if (type) updateData['commission.type'] = type;

    const vendor = await Vendor.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('user', 'firstName lastName email');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      message: 'Vendor commission updated successfully',
      data: { vendor }
    });

  } catch (error) {
    console.error('Update commission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update commission',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get vendor statistics
const getVendorStatistics = async (req, res) => {
  try {
    const stats = await Vendor.getStatistics();
    
    // Get registration trends for the last 30 days
    const registrationTrends = await Vendor.getRecentRegistrations(30);
    
    // Get top performing vendors
    const topPerformers = await Vendor.getTopPerformers(10);

    // Get verification status distribution
    const verificationDistribution = await Vendor.aggregate([
      {
        $group: {
          _id: '$verification.status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get subscription distribution
    const subscriptionDistribution = await Vendor.aggregate([
      {
        $group: {
          _id: '$subscription.plan',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$performance.totalRevenue' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        overview: stats,
        trends: registrationTrends,
        topPerformers,
        distributions: {
          verification: verificationDistribution,
          subscription: subscriptionDistribution
        }
      }
    });

  } catch (error) {
    console.error('Get vendor statistics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete vendor
const deleteVendor = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if vendor has products
    const productCount = await Product.countDocuments({ vendor: id });
    if (productCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete vendor with existing products. Consider deactivating instead.'
      });
    }

    // Check if vendor has orders
    const orderCount = await Order.countDocuments({ 'items.vendor': id });
    if (orderCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete vendor with existing orders. Consider deactivating instead.'
      });
    }

    const vendor = await Vendor.findByIdAndDelete(id);

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      message: 'Vendor deleted successfully'
    });

  } catch (error) {
    console.error('Delete vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete vendor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Bulk update vendors
const bulkUpdateVendors = async (req, res) => {
  try {
    const { vendorIds, updates } = req.body;

    if (!Array.isArray(vendorIds) || vendorIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Vendor IDs array is required'
      });
    }

    // Remove sensitive fields
    delete updates._id;
    delete updates.user;
    delete updates.createdAt;
    delete updates.updatedAt;

    const result = await Vendor.updateMany(
      { _id: { $in: vendorIds } },
      updates,
      { runValidators: true }
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} vendors updated successfully`,
      data: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount
      }
    });

  } catch (error) {
    console.error('Bulk update vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update vendors',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get vendors pending verification
const getPendingVerification = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const vendors = await Vendor.find({ 'verification.status': 'pending' })
      .populate('user', 'firstName lastName email')
      .sort({ createdAt: 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    const totalVendors = await Vendor.countDocuments({ 'verification.status': 'pending' });
    const totalPages = Math.ceil(totalVendors / limit);

    res.json({
      success: true,
      data: {
        vendors,
        pagination: {
          currentPage: page,
          totalPages,
          totalVendors,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });

  } catch (error) {
    console.error('Get pending verification vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending verification vendors',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verify vendor
const verifyVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;
    const adminId = req.user.userId;

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    if (vendor.verification.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Vendor is not pending verification'
      });
    }

    await vendor.verifyVendor(adminId, notes);

    // TODO: Send notification to vendor

    res.json({
      success: true,
      message: 'Vendor verified successfully',
      data: { vendor }
    });

  } catch (error) {
    console.error('Verify vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify vendor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Reject vendor verification
const rejectVendorVerification = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason, notes } = req.body;
    const adminId = req.user.userId;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    if (vendor.verification.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Vendor is not pending verification'
      });
    }

    await vendor.rejectVerification(adminId, reason, notes);

    // TODO: Send notification to vendor

    res.json({
      success: true,
      message: 'Vendor verification rejected',
      data: { vendor }
    });

  } catch (error) {
    console.error('Reject vendor verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject vendor verification',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Process vendor payout
const processVendorPayout = async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, method, transactionId } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid payout amount is required'
      });
    }

    if (!method || !['bank_transfer', 'paypal', 'stripe', 'check'].includes(method)) {
      return res.status(400).json({
        success: false,
        message: 'Valid payout method is required'
      });
    }

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    if (amount > vendor.commission.pendingAmount) {
      return res.status(400).json({
        success: false,
        message: 'Payout amount exceeds pending commission'
      });
    }

    await vendor.recordPayout(amount, method, transactionId);

    res.json({
      success: true,
      message: 'Payout processed successfully',
      data: { vendor }
    });

  } catch (error) {
    console.error('Process vendor payout error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process payout',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get vendor commission report
const getVendorCommissionReport = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const matchStage = {};
    if (startDate && endDate) {
      matchStage.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const report = await Vendor.aggregate([
      { $match: matchStage },
      {
        $project: {
          businessName: 1,
          'commission.totalEarned': 1,
          'commission.totalPaid': 1,
          'commission.pendingAmount': 1,
          'commission.rate': 1,
          'performance.totalRevenue': 1,
          'performance.totalOrders': 1,
          status: 1
        }
      },
      {
        $group: {
          _id: null,
          totalCommissionEarned: { $sum: '$commission.totalEarned' },
          totalCommissionPaid: { $sum: '$commission.totalPaid' },
          totalPendingCommission: { $sum: '$commission.pendingAmount' },
          totalVendorRevenue: { $sum: '$performance.totalRevenue' },
          vendors: { $push: '$$ROOT' }
        }
      }
    ]);

    res.json({
      success: true,
      data: report[0] || {
        totalCommissionEarned: 0,
        totalCommissionPaid: 0,
        totalPendingCommission: 0,
        totalVendorRevenue: 0,
        vendors: []
      }
    });

  } catch (error) {
    console.error('Get vendor commission report error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate commission report',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Approve vendor (shortcut for verification and status update)
const approveVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const adminId = req.user.id;

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Use the vendor model's verifyVendor method
    await vendor.verifyVendor(adminId);

    res.json({
      success: true,
      message: 'Vendor approved successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Approve vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve vendor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Suspend vendor
const suspendVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Update vendor status to suspended
    vendor.status = 'suspended';
    vendor.verification.status = 'suspended';
    if (reason) {
      vendor.verification.rejectionReason = reason;
    }
    await vendor.save();

    // Deactivate all vendor products
    await Product.updateMany(
      { vendor: id },
      { status: 'inactive' }
    );

    res.json({
      success: true,
      message: 'Vendor suspended successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Suspend vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to suspend vendor',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getVendors,
  getVendorById,
  updateVendor,
  updateVendorStatus,
  updateVerificationStatus,
  updateCommission,
  getVendorStatistics,
  deleteVendor,
  bulkUpdateVendors,
  getPendingVerification,
  verifyVendor,
  rejectVendorVerification,
  processVendorPayout,
  getVendorCommissionReport,
  approveVendor,
  suspendVendor
};