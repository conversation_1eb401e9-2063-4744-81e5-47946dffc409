#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Import the User model - using the comprehensive schema
const User = require('./schema/userSchema');

// Admin credentials
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password@admin123'
};

// Colors for better console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m',
  reset: '\x1b[0m'
};

console.log(`${colors.bold}${colors.blue}🚀 Admin Account Creator${colors.reset}`);
console.log(`${colors.cyan}Creating super admin account with full access...${colors.reset}\n`);

async function connectToDatabase() {
  try {
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
    await mongoose.connect(uri);
    console.log(`${colors.green}✅ Connected to MongoDB${colors.reset}`);
    console.log(`${colors.yellow}📍 Database: ${uri}${colors.reset}\n`);
    return true;
  } catch (error) {
    console.error(`${colors.red}❌ Database connection failed: ${error.message}${colors.reset}`);
    return false;
  }
}

async function createAdminAccount() {
  try {
    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: ADMIN_CREDENTIALS.email });
    
    if (existingAdmin) {
      console.log(`${colors.yellow}⚠️  Admin account already exists!${colors.reset}`);
      console.log(`${colors.cyan}📧 Email: ${existingAdmin.email}${colors.reset}`);
      console.log(`${colors.cyan}👤 Type: ${existingAdmin.userType}${colors.reset}`);
      console.log(`${colors.cyan}🔐 Role: ${existingAdmin.role}${colors.reset}`);
      
      // Update existing user to ensure it has admin privileges
      existingAdmin.userType = 'admin';
      existingAdmin.role = 'super_admin';
      existingAdmin.emailVerification.isVerified = true;
      existingAdmin.emailVerification.verifiedAt = new Date();
      existingAdmin.isActive = true;
      existingAdmin.isBlocked = false;
      
      // Update password if needed (will be hashed by pre-save middleware)
      existingAdmin.password = ADMIN_CREDENTIALS.password;
      
      await existingAdmin.save();
      console.log(`${colors.green}✅ Admin account updated with super admin privileges!${colors.reset}\n`);
      return existingAdmin;
    }

    // Create new admin account
    const adminData = {
      // Basic Information (not required for admin but good to have)
      firstName: 'Super',
      lastName: 'Admin',
      email: ADMIN_CREDENTIALS.email,
      password: ADMIN_CREDENTIALS.password, // Will be hashed by pre-save middleware
      
      // User Type and Role - CRITICAL for admin access
      userType: 'admin',
      role: 'super_admin',
      
      // Contact Information
      phone: '+**********',
      countryCode: 'US',
      
      // Address Information
      address: 'Admin Office',
      city: 'Admin City',
      state: 'Admin State',
      zipCode: '12345',
      country: 'United States',
      
      // Account Status - CRITICAL
      isActive: true,
      isBlocked: false,
      
      // Email Verification - CRITICAL for login
      emailVerification: {
        isVerified: true,
        verifiedAt: new Date()
      },
      
      // Security Settings
      security: {
        lastLogin: null,
        lastLoginIP: null,
        loginAttempts: 0,
        lockUntil: null,
        passwordChangedAt: new Date()
      },
      
      // Two-Factor Authentication (disabled for ease of access)
      twoFactorAuth: {
        isEnabled: false
      },
      
      // Social Authentication (disabled)
      socialAuth: {
        isEnabled: false,
        providers: []
      },
      
      // User Preferences
      preferences: {
        language: 'en',
        timezone: 'UTC',
        currency: 'USD',
        notifications: {
          email: {
            marketing: true,
            orderUpdates: true,
            security: true
          },
          sms: {
            marketing: false,
            orderUpdates: true,
            security: true
          },
          push: {
            marketing: true,
            orderUpdates: true,
            security: true
          }
        }
      },
      
      // Statistics (initialized)
      statistics: {
        totalOrders: 0,
        totalSpent: 0,
        totalSaved: 0,
        favoriteCategories: [],
        lastOrderDate: null,
        averageOrderValue: 0
      },
      
      // Timestamps
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActiveAt: new Date()
    };

    const adminUser = new User(adminData);
    await adminUser.save();

    console.log(`${colors.green}🎉 Admin account created successfully!${colors.reset}\n`);
    return adminUser;

  } catch (error) {
    console.error(`${colors.red}❌ Error creating admin account: ${error.message}${colors.reset}`);
    throw error;
  }
}

async function verifyAdminAccount(adminUser) {
  try {
    console.log(`${colors.bold}${colors.cyan}🔍 Verifying Admin Account Details:${colors.reset}`);
    console.log(`${colors.bold}=================================${colors.reset}`);
    console.log(`${colors.bold}📧 Email:${colors.reset} ${adminUser.email}`);
    console.log(`${colors.bold}👤 User Type:${colors.reset} ${colors.green}${adminUser.userType}${colors.reset}`);
    console.log(`${colors.bold}🔐 Role:${colors.reset} ${colors.green}${adminUser.role}${colors.reset}`);
    console.log(`${colors.bold}✅ Status:${colors.reset} ${adminUser.isActive ? colors.green + 'Active' : colors.red + 'Inactive'}${colors.reset}`);
    console.log(`${colors.bold}🚫 Blocked:${colors.reset} ${adminUser.isBlocked ? colors.red + 'Yes' : colors.green + 'No'}${colors.reset}`);
    console.log(`${colors.bold}📬 Email Verified:${colors.reset} ${adminUser.emailVerification.isVerified ? colors.green + 'Yes' : colors.red + 'No'}${colors.reset}`);
    console.log(`${colors.bold}🔒 2FA Enabled:${colors.reset} ${adminUser.twoFactorAuth.isEnabled ? colors.yellow + 'Yes' : colors.green + 'No (Good for testing)'}${colors.reset}`);
    console.log(`${colors.bold}🆔 User ID:${colors.reset} ${adminUser._id}`);
    console.log(`${colors.bold}📅 Created:${colors.reset} ${adminUser.createdAt.toLocaleString()}`);
    
    // Test password verification
    console.log(`\n${colors.bold}${colors.yellow}🔐 Testing Password Verification...${colors.reset}`);
    const isPasswordValid = await adminUser.comparePassword(ADMIN_CREDENTIALS.password);
    console.log(`${colors.bold}Password Test:${colors.reset} ${isPasswordValid ? colors.green + 'PASSED ✅' : colors.red + 'FAILED ❌'}${colors.reset}`);
    
    console.log(`${colors.bold}=================================${colors.reset}\n`);
    
  } catch (error) {
    console.error(`${colors.red}❌ Error verifying admin account: ${error.message}${colors.reset}`);
  }
}

async function showLoginInstructions() {
  console.log(`${colors.bold}${colors.green}🎯 Admin Account Ready!${colors.reset}`);
  console.log(`${colors.bold}========================${colors.reset}`);
  console.log(`${colors.bold}📧 Email:${colors.reset} ${colors.cyan}${ADMIN_CREDENTIALS.email}${colors.reset}`);
  console.log(`${colors.bold}🔑 Password:${colors.reset} ${colors.cyan}${ADMIN_CREDENTIALS.password}${colors.reset}`);
  console.log(`${colors.bold}========================${colors.reset}\n`);
  
  console.log(`${colors.bold}${colors.blue}🌐 Access Points:${colors.reset}`);
  console.log(`${colors.yellow}Frontend URL:${colors.reset} ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
  console.log(`${colors.yellow}Backend API:${colors.reset} http://localhost:${process.env.PORT || 5000}/api`);
  console.log(`${colors.yellow}Admin Panel:${colors.reset} ${process.env.FRONTEND_URL || 'http://localhost:5173'}/admin`);
  console.log(`${colors.yellow}Health Check:${colors.reset} http://localhost:${process.env.PORT || 5000}/api/health\n`);
  
  console.log(`${colors.bold}${colors.green}✨ Admin Privileges:${colors.reset}`);
  console.log(`${colors.green}• Full access to Admin Dashboard${colors.reset}`);
  console.log(`${colors.green}• User Management (View, Edit, Block, Delete)${colors.reset}`);
  console.log(`${colors.green}• Vendor Management (Approve, Reject, Monitor)${colors.reset}`); 
  console.log(`${colors.green}• Product Management (View, Edit, Approve, Remove)${colors.reset}`);
  console.log(`${colors.green}• Order Management (View, Update Status, Refunds)${colors.reset}`);
  console.log(`${colors.green}• Analytics and Reports${colors.reset}`);
  console.log(`${colors.green}• System Settings and Configuration${colors.reset}`);
  console.log(`${colors.green}• Homepage and Content Management${colors.reset}`);
  console.log(`${colors.green}• Category Management${colors.reset}`);
  console.log(`${colors.green}• Notification Management${colors.reset}\n`);
  
  console.log(`${colors.bold}${colors.cyan}🚀 You can now:${colors.reset}`);
  console.log(`${colors.cyan}1. Start your frontend application${colors.reset}`);
  console.log(`${colors.cyan}2. Navigate to the admin login page${colors.reset}`);
  console.log(`${colors.cyan}3. Login with the credentials above${colors.reset}`);
  console.log(`${colors.cyan}4. Access all admin features and panels${colors.reset}\n`);
  
  console.log(`${colors.bold}${colors.yellow}⚠️  Security Note:${colors.reset}`);
  console.log(`${colors.yellow}Remember to change the admin password in production!${colors.reset}\n`);
}

// Main execution function
async function main() {
  try {
    // Connect to database
    const connected = await connectToDatabase();
    if (!connected) {
      process.exit(1);
    }

    // Create admin account
    const adminUser = await createAdminAccount();
    
    // Verify the account was created correctly
    await verifyAdminAccount(adminUser);
    
    // Show login instructions
    await showLoginInstructions();
    
    console.log(`${colors.bold}${colors.green}🎉 Admin account setup completed successfully!${colors.reset}`);
    
  } catch (error) {
    console.error(`${colors.red}❌ Script failed: ${error.message}${colors.reset}`);
    console.error(`${colors.red}Stack trace: ${error.stack}${colors.reset}`);
    process.exit(1);
  } finally {
    // Close database connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log(`${colors.yellow}📴 Database connection closed${colors.reset}`);
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log(`\n${colors.yellow}👋 Shutting down gracefully...${colors.reset}`);
  if (mongoose.connection.readyState === 1) {
    await mongoose.disconnect();
  }
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  createAdminAccount,
  ADMIN_CREDENTIALS
};
